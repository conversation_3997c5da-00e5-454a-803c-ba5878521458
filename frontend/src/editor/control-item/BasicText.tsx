import FormatAlignCenterIcon from "@mui/icons-material/FormatAlignCenter";
import FormatAlignLeftIcon from "@mui/icons-material/FormatAlignLeft";
import FormatAlignRightIcon from "@mui/icons-material/FormatAlignRight";
import FormatBoldIcon from "@mui/icons-material/FormatBold";
import FormatItalicIcon from "@mui/icons-material/FormatItalic";
import FormatUnderlinedIcon from "@mui/icons-material/FormatUnderlined";
import StrikethroughSIcon from "@mui/icons-material/StrikethroughS";
import { Divider, SelectChangeEvent, Stack } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useState,
  useCallback,
  useMemo,
  useContext,
  useEffect,
} from "react";
import {
  ControlPanel,
  ColorRow,
  ToggleButtonRow,
  SelectRow,
} from "../../components/common";
import { GradientPicker } from "../components/color/GradientPicker";
import BaseSetting from "./BaseSetting";
import { StoreContext } from "../../store";
import { EditorElement, TextEditorElement } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";
import SliderWithInput from "./SliderWithInput";

// 常量定义 - 使用操作系统常见字体
const FONT_FAMILIES = [
  // Windows/macOS/Linux 通用字体
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Courier New",
  "Verdana",
  "Georgia",
  "Trebuchet MS",
  "Arial Black",
  "Impact",
  "Comic Sans MS",
  "Tahoma",
  // 现代系统字体
  "Roboto",
  "Segoe UI",
  "Helvetica Neue",
  "Liberation Sans",
  // macOS 系统字体
  "San Francisco",
  "Lucida Grande",
  "PingFang SC",
  // Windows 系统字体
  "Microsoft YaHei",
  "SimSun",
  "SimHei",
  // Linux 系统字体
  "Ubuntu",
  "DejaVu Sans",
  // 等宽字体
  "Courier",
  "Monaco",
  "Consolas",
  "Menlo",
] as const;

const DEFAULT_TEXT_STYLE = {
  fontSize: 100,
  textAlign: "left" as "left" | "center" | "right",
  styles: [] as string[],
  charSpacing: 0,
  lineHeight: 1,
  fontColor: "#ffffff",
  fontFamily: "Arial",
  strokeWidth: 0,
  strokeColor: "#000000",
  shadowColor: "#000000",
  shadowBlur: 0,
  shadowOffsetX: 0,
  shadowOffsetY: 0,
  gradientColors: ["#ffffff", "#000000"],
  useGradient: false,
  backgroundColor: "transparent",
};

interface BasicTextProps {
  // 不再需要传入特定的element，因为我们使用全局store的selectedElement
}

const BasicText = observer(({}: BasicTextProps) => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();

  // 获取全局选中的元素 - 参考BaseSetting的模式
  const element = store.selectedElement;

  // 细粒度状态管理 - 参考CaptionText的模式，为每个属性单独使用useState
  const [fontSize, setFontSize] = useState(DEFAULT_TEXT_STYLE.fontSize);
  const [textAlign, setTextAlign] = useState(DEFAULT_TEXT_STYLE.textAlign);
  const [styles, setStyles] = useState(DEFAULT_TEXT_STYLE.styles);
  const [charSpacing, setCharSpacing] = useState(
    DEFAULT_TEXT_STYLE.charSpacing
  );
  const [lineHeight, setLineHeight] = useState(DEFAULT_TEXT_STYLE.lineHeight);
  const [fontColor, setFontColor] = useState(DEFAULT_TEXT_STYLE.fontColor);
  const [fontFamily, setFontFamily] = useState(DEFAULT_TEXT_STYLE.fontFamily);
  const [strokeWidth, setStrokeWidth] = useState(
    DEFAULT_TEXT_STYLE.strokeWidth
  );
  const [strokeColor, setStrokeColor] = useState(
    DEFAULT_TEXT_STYLE.strokeColor
  );
  const [shadowColor, setShadowColor] = useState(
    DEFAULT_TEXT_STYLE.shadowColor
  );
  const [shadowBlur, setShadowBlur] = useState(DEFAULT_TEXT_STYLE.shadowBlur);
  const [shadowOffsetX, setShadowOffsetX] = useState(
    DEFAULT_TEXT_STYLE.shadowOffsetX
  );
  const [shadowOffsetY, setShadowOffsetY] = useState(
    DEFAULT_TEXT_STYLE.shadowOffsetY
  );
  const [gradientColors, setGradientColors] = useState(
    DEFAULT_TEXT_STYLE.gradientColors
  );
  const [useGradient, setUseGradient] = useState(
    DEFAULT_TEXT_STYLE.useGradient
  );
  const [backgroundColor, setBackgroundColor] = useState(
    DEFAULT_TEXT_STYLE.backgroundColor
  );

  // 获取当前文本元素
  const textElement = useMemo(() => {
    if (!element || element.type !== "text") return null;
    return element as TextEditorElement;
  }, [element]);

  // 通用的样式更新函数 - 参考BaseSetting的即时更新模式
  const updateTextStyleAndStore = useCallback(
    (updates: Partial<typeof DEFAULT_TEXT_STYLE>) => {
      if (!textElement) return;

      // 更新store状态
      store.updateTextStyle(textElement.id, updates);
    },
    [textElement, store]
  );

  // 同步全局选中元素状态变化 - 使用更细粒度的依赖监听和状态更新
  useEffect(() => {
    if (textElement?.properties) {
      const props = textElement.properties;
      setFontSize(props.fontSize || DEFAULT_TEXT_STYLE.fontSize);
      setTextAlign(props.textAlign || DEFAULT_TEXT_STYLE.textAlign);
      setStyles(props.styles || DEFAULT_TEXT_STYLE.styles);
      setCharSpacing(props.charSpacing || DEFAULT_TEXT_STYLE.charSpacing);
      setLineHeight(props.lineHeight || DEFAULT_TEXT_STYLE.lineHeight);
      setFontColor(props.fontColor || DEFAULT_TEXT_STYLE.fontColor);
      setFontFamily(props.fontFamily || DEFAULT_TEXT_STYLE.fontFamily);
      setStrokeWidth(props.strokeWidth || DEFAULT_TEXT_STYLE.strokeWidth);
      setStrokeColor(props.strokeColor || DEFAULT_TEXT_STYLE.strokeColor);
      setShadowColor(props.shadowColor || DEFAULT_TEXT_STYLE.shadowColor);
      setShadowBlur(props.shadowBlur || DEFAULT_TEXT_STYLE.shadowBlur);
      setShadowOffsetX(props.shadowOffsetX || DEFAULT_TEXT_STYLE.shadowOffsetX);
      setShadowOffsetY(props.shadowOffsetY || DEFAULT_TEXT_STYLE.shadowOffsetY);
      setGradientColors(
        props.gradientColors || DEFAULT_TEXT_STYLE.gradientColors
      );
      setUseGradient(props.useGradient || DEFAULT_TEXT_STYLE.useGradient);
      setBackgroundColor(
        props.backgroundColor || DEFAULT_TEXT_STYLE.backgroundColor
      );
    }
  }, [
    store.selectedElement,
    // 添加具体的元素属性作为依赖，确保属性变化时能触发更新
    store.selectedElement?.type,
    store.selectedElement?.properties,
    textElement?.properties?.fontSize,
    textElement?.properties?.textAlign,
    textElement?.properties?.styles,
    textElement?.properties?.charSpacing,
    textElement?.properties?.lineHeight,
    textElement?.properties?.fontColor,
    textElement?.properties?.fontFamily,
    textElement?.properties?.strokeWidth,
    textElement?.properties?.strokeColor,
    textElement?.properties?.shadowColor,
    textElement?.properties?.shadowBlur,
    textElement?.properties?.shadowOffsetX,
    textElement?.properties?.shadowOffsetY,
    textElement?.properties?.gradientColors,
    textElement?.properties?.useGradient,
    textElement?.properties?.backgroundColor,
  ]);

  // 事件处理函数 - 使用细粒度状态更新
  const handleAlignmentChange = useCallback(
    (event: React.MouseEvent<HTMLElement>, newAlignment: string | null) => {
      if (newAlignment !== null) {
        const alignmentValue = newAlignment as "left" | "center" | "right";
        setTextAlign(alignmentValue);
        updateTextStyleAndStore({ textAlign: alignmentValue });
      }
    },
    [updateTextStyleAndStore]
  );

  const handleStyleChange = useCallback(
    (event: React.MouseEvent<HTMLElement>, newStyles: string[]) => {
      setStyles(newStyles);
      updateTextStyleAndStore({ styles: newStyles });
    },
    [updateTextStyleAndStore]
  );

  const handleColorChange = useCallback(
    (color: string) => {
      if (useGradient) {
        const newGradientColors = [...gradientColors];
        newGradientColors[0] = color;
        setGradientColors(newGradientColors);
        updateTextStyleAndStore({
          gradientColors: newGradientColors,
          useGradient: true,
        });
      } else {
        setFontColor(color);
        updateTextStyleAndStore({
          fontColor: color,
          useGradient: false,
        });
      }
    },
    [useGradient, gradientColors, updateTextStyleAndStore]
  );

  const handleGradientChange = useCallback(
    (newGradientColors: string[]) => {
      setGradientColors(newGradientColors);
      setUseGradient(true);
      updateTextStyleAndStore({
        gradientColors: newGradientColors,
        useGradient: true,
      });
    },
    [updateTextStyleAndStore]
  );

  const handleFontFamilyChange = useCallback(
    (event: SelectChangeEvent<unknown>) => {
      const newFontFamily = event.target.value as string;
      setFontFamily(newFontFamily);
      updateTextStyleAndStore({ fontFamily: newFontFamily });
    },
    [updateTextStyleAndStore]
  );

  // 滑块组件配置 - 使用细粒度状态
  const sliderConfigs = useMemo(
    () => [
      {
        key: "fontSize",
        label: t("font_size"),
        value: fontSize,
        min: 0,
        max: 200,
        step: 1,
      },
      {
        key: "charSpacing",
        label: t("char_spacing"),
        value: charSpacing,
        min: -200,
        max: 800,
        step: 0.1,
      },
      {
        key: "lineHeight",
        label: t("line_height"),
        value: lineHeight,
        min: -2,
        max: 2,
        step: 0.01,
      },
    ],
    [fontSize, charSpacing, lineHeight, t]
  );

  const advancedSliderConfigs = useMemo(
    () => [
      {
        key: "strokeWidth",
        label: t("stroke_width"),
        value: strokeWidth,
        min: 0,
        max: 5,
        step: 0.2,
      },
      {
        key: "shadowBlur",
        label: t("shadow_blur"),
        value: shadowBlur,
        min: 0,
        max: 10,
        step: 1,
      },
      {
        key: "shadowOffsetX",
        label: t("shadow_offset_x"),
        value: shadowOffsetX,
        min: -50,
        max: 50,
        step: 1,
      },
      {
        key: "shadowOffsetY",
        label: t("shadow_offset_y"),
        value: shadowOffsetY,
        min: -20,
        max: 20,
        step: 1,
      },
    ],
    [strokeWidth, shadowBlur, shadowOffsetX, shadowOffsetY, t]
  );

  // 早期返回检查
  if (!textElement) {
    return <></>;
  }

  // 基础选项卡内容
  const basicTabContent = (
    <>
      <BaseSetting />

      <SelectRow
        label={t("font_family")}
        value={fontFamily}
        onChange={handleFontFamilyChange}
        options={FONT_FAMILIES.map((font) => ({
          value: font,
          label: font,
          preview: font,
        }))}
      />

      <ColorRow
        label={t("font_color")}
        color={useGradient ? gradientColors : fontColor}
        onChange={handleColorChange}
        isGradient={useGradient}
      />

      <ColorRow
        label={t("background")}
        color={backgroundColor}
        onChange={(color) => {
          setBackgroundColor(color as string);
          updateTextStyleAndStore({ backgroundColor: color as string });
        }}
      />

      <ToggleButtonRow
        label={t("text_align")}
        value={textAlign}
        onChange={handleAlignmentChange}
        buttons={[
          {
            value: "left",
            icon: <FormatAlignLeftIcon fontSize="small" />,
            label: "left aligned",
          },
          {
            value: "center",
            icon: <FormatAlignCenterIcon fontSize="small" />,
            label: "centered",
          },
          {
            value: "right",
            icon: <FormatAlignRightIcon fontSize="small" />,
            label: "right aligned",
          },
        ]}
      />

      <Divider />

      <ToggleButtonRow
        label={t("styles")}
        value={styles}
        onChange={handleStyleChange}
        exclusive={false}
        buttons={[
          {
            value: "bold",
            icon: <FormatBoldIcon fontSize="small" />,
            label: "bold",
          },
          {
            value: "italic",
            icon: <FormatItalicIcon fontSize="small" />,
            label: "italic",
          },
          {
            value: "underlined",
            icon: <FormatUnderlinedIcon fontSize="small" />,
            label: "underlined",
          },
          {
            value: "strikethrough",
            icon: <StrikethroughSIcon fontSize="small" />,
            label: "strikethrough",
          },
        ]}
      />

      <Divider />

      {/* 滑块控件 */}
      {sliderConfigs.map((config) => (
        <SliderWithInput
          key={config.key}
          label={config.label}
          value={config.value}
          onChange={(newValue) => {
            // 立即更新对应的本地状态
            if (config.key === "fontSize") setFontSize(newValue);
            else if (config.key === "charSpacing") setCharSpacing(newValue);
            else if (config.key === "lineHeight") setLineHeight(newValue);
          }}
          onChangeCommitted={(newValue) =>
            updateTextStyleAndStore({ [config.key]: newValue })
          }
          min={config.min}
          max={config.max}
          step={config.step}
        />
      ))}
    </>
  );

  // 高级选项卡内容
  const advancedTabContent = (
    <>
      {/* 高级滑块控件 */}
      {advancedSliderConfigs.map((config) => (
        <SliderWithInput
          key={config.key}
          label={config.label}
          value={config.value}
          onChange={(newValue) => {
            // 立即更新对应的本地状态
            if (config.key === "strokeWidth") setStrokeWidth(newValue);
            else if (config.key === "shadowBlur") setShadowBlur(newValue);
            else if (config.key === "shadowOffsetX") setShadowOffsetX(newValue);
            else if (config.key === "shadowOffsetY") setShadowOffsetY(newValue);
          }}
          onChangeCommitted={(newValue) =>
            updateTextStyleAndStore({ [config.key]: newValue })
          }
          min={config.min}
          max={config.max}
          step={config.step}
        />
      ))}

      <ColorRow
        label={t("stroke_color")}
        color={strokeColor}
        onChange={(color) => {
          setStrokeColor(color as string);
          updateTextStyleAndStore({ strokeColor: color as string });
        }}
      />

      <Divider />

      <ColorRow
        label={t("shadow_color")}
        color={shadowColor}
        onChange={(color) => {
          setShadowColor(color as string);
          updateTextStyleAndStore({ shadowColor: color as string });
        }}
      />
    </>
  );

  return (
    <ControlPanel
      title={t("text")}
      tabs={[
        { label: t("basic"), content: basicTabContent },
        { label: t("advanced"), content: advancedTabContent },
      ]}
    />
  );
});

export default BasicText;
