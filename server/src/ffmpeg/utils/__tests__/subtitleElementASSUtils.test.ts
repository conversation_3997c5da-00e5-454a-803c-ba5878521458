import { SubtitleElementASSUtils } from "../subtitleElementASSUtils";
import { Caption, CaptionStyle } from "../../types";
import * as fs from "fs";

describe("SubtitleElementASSUtils", () => {
  const mockCaptions: Caption[] = [
    {
      id: "caption1",
      text: "Hello World",
      startTime: "00:00:01",
      endTime: "00:00:03",
    },
    {
      id: "caption2",
      text: "This is a test subtitle",
      startTime: "00:00:05",
      endTime: "00:00:08",
    },
  ];

  const mockStyle: CaptionStyle = {
    fontSize: 24,
    fontFamily: "Arial",
    fontColor: "#FFFFFF",
    fontWeight: 400,
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
    styles: ["bold", "italic"],
    strokeWidth: 2,
    strokeColor: "#000000",
    shadowColor: "#808080",
    shadowBlur: 2,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    backgroundColor: "#FF0000",
    useGradient: false,
    gradientColors: ["#FFFFFF", "#CCCCCC"],
  };

  describe("createSubtitleElementASSFile", () => {
    it("should create ASS file with basic style properties", () => {
      const assFilePath = SubtitleElementASSUtils.createSubtitleElementASSFile(
        mockCaptions,
        mockStyle,
        1920,
        1080
      );

      expect(assFilePath).toBeTruthy();
      expect(fs.existsSync(assFilePath)).toBe(true);

      const content = fs.readFileSync(assFilePath, "utf8");

      // 验证ASS文件结构
      expect(content).toContain("[Script Info]");
      expect(content).toContain("[V4+ Styles]");
      expect(content).toContain("[Events]");

      // 验证字幕内容
      expect(content).toContain("Hello World");
      expect(content).toContain("This is a test subtitle");

      // 清理
      fs.unlinkSync(assFilePath);
    });

    it("should handle empty captions array", () => {
      expect(() => {
        SubtitleElementASSUtils.createSubtitleElementASSFile(
          [],
          mockStyle,
          1920,
          1080
        );
      }).toThrow("字幕数据不能为空");
    });
  });
});
