import * as fs from "fs";
import * as path from "path";
import { Caption, CaptionStyle } from "../types";

/**
 * 字幕元素ASS处理工具类
 * 提供与文本元素相同的完整样式属性支持
 */
export class SubtitleElementASSUtils {
  /**
   * 创建字幕元素的ASS文件
   * 支持与文本元素相同的所有样式属性
   */
  static createSubtitleElementASSFile(
    captions: Caption[],
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): string {
    if (!captions || captions.length === 0) {
      throw new Error("字幕数据不能为空");
    }

    // 生成ASS内容
    const assContent = this.generateSubtitleElementASSContent(
      captions,
      style,
      canvasWidth,
      canvasHeight
    );

    // 创建临时文件
    const tempDir = path.join(process.cwd(), "temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const fileName = `subtitle_element_${Date.now()}_${Math.random()
      .toString(36)
      .substring(7)}.ass`;
    const filePath = path.join(tempDir, fileName);

    fs.writeFileSync(filePath, assContent, "utf8");

    return filePath;
  }

  /**
   * 生成字幕元素的ASS内容
   */
  private static generateSubtitleElementASSContent(
    captions: Caption[],
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): string {
    let content = this.generateASSHeader(canvasWidth, canvasHeight);

    // 添加样式定义
    content += this.generateStyleDefinition(style, canvasWidth, canvasHeight);

    // 添加事件部分
    content += "\n[Events]\n";
    content +=
      "Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n";

    // 处理每个字幕
    captions.forEach((caption) => {
      // 如果有背景色，先渲染背景层
      if (style.backgroundColor && style.backgroundColor !== "transparent") {
        const backgroundDialogue = this.generateBackgroundDialogue(
          caption,
          style,
          canvasWidth,
          canvasHeight
        );
        content += backgroundDialogue + "\n";
      }

      // 渲染文字层
      const textDialogue = this.generateTextDialogue(
        caption,
        style,
        canvasWidth,
        canvasHeight
      );
      content += textDialogue + "\n";
    });

    return content;
  }

  /**
   * 生成背景对话行
   */
  private static generateBackgroundDialogue(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): string {
    const startTime = this.convertTimeToASS(caption.startTime);
    const endTime = this.convertTimeToASS(caption.endTime);

    // 计算背景矩形的位置和大小
    // 优先使用前端传来的width属性，考虑缩放因子
    let backgroundWidth: number;
    if (style.width !== undefined) {
      const scaleX = style.scaleX || 1;
      backgroundWidth = style.width * scaleX;
      console.log(
        `字幕背景框使用前端宽度 (考虑缩放${scaleX}): ${style.width} -> ${backgroundWidth}`
      );
    } else {
      // 回退到估算文本宽度
      backgroundWidth = this.estimateTextWidth(caption.text, style);
      console.log(`字幕背景框使用估算宽度: ${backgroundWidth}`);
    }

    const scaleY = style.scaleY || 1;
    const textHeight = style.fontSize * (style.lineHeight || 1.2) * scaleY;

    // 计算位置
    const centerX = canvasWidth / 2 + (style.positionX || 0);
    const centerY = canvasHeight * 0.85 + (style.positionY || 0);

    // 背景矩形坐标（添加内边距）
    const padding = 10;
    const rectLeft = centerX - backgroundWidth / 2 - padding;
    const rectTop = centerY - textHeight / 2 - padding / 2;
    const rectRight = centerX + backgroundWidth / 2 + padding;
    const rectBottom = centerY + textHeight / 2 + padding / 2;

    // 生成ASS绘图命令 - 使用矩形绘制背景
    const assColor = this.convertColorToASS(style.backgroundColor);

    // ASS绘图格式：设置颜色并绘制圆角矩形
    // \1c 设置主要颜色，\3c 设置边框颜色（设为相同颜色避免边框），\p1 开始绘图模式
    const borderRadius = 8; // 圆角半径，与前端保持一致

    // 处理透明度（如果颜色包含alpha通道）
    let alphaTag = "";
    if (style.backgroundColor.startsWith("rgba(")) {
      const rgbaMatch = style.backgroundColor.match(
        /rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/
      );
      if (rgbaMatch) {
        const alpha = parseFloat(rgbaMatch[4]);
        if (alpha < 1) {
          const assAlpha = Math.round((1 - alpha) * 255);
          const alphaHex = assAlpha.toString(16).padStart(2, "0").toUpperCase();
          alphaTag = `\\1a&H${alphaHex}&`;
        }
      }
    }

    const drawingCommands = this.generateRoundedRectangleDrawing(
      rectLeft,
      rectTop,
      rectRight,
      rectBottom,
      borderRadius,
      assColor,
      alphaTag
    );

    console.log(
      `字幕背景框颜色设置: ${style.backgroundColor} -> ASS: ${assColor}`
    );

    return `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${drawingCommands}`;
  }

  /**
   * 生成文字对话行
   */
  private static generateTextDialogue(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): string {
    const startTime = this.convertTimeToASS(caption.startTime);
    const endTime = this.convertTimeToASS(caption.endTime);

    // 生成样式标签
    const styleTags = this.buildCompleteStyleTags(
      style,
      canvasWidth,
      canvasHeight
    );

    // 处理文本内容
    const processedText = this.processTextContent(caption.text);

    return `Dialogue: 1,${startTime},${endTime},Default,,0,0,0,,${styleTags}${processedText}`;
  }

  /**
   * 转换时间格式为ASS格式
   */
  private static convertTimeToASS(timeStr: string): string {
    // 输入格式: HH:MM:SS
    // 输出格式: H:MM:SS.CC
    const parts = timeStr.split(":");
    const hours = parseInt(parts[0]);
    const minutes = parts[1];
    const seconds = parts[2];

    return `${hours}:${minutes}:${seconds}.00`;
  }

  /**
   * 估算文本宽度
   */
  private static estimateTextWidth(text: string, style: CaptionStyle): number {
    // 简单的文本宽度估算
    const charWidth = style.fontSize * 0.6; // 大致估算
    return text.length * charWidth * (1 + (style.charSpacing || 0) / 100);
  }

  /**
   * 处理文本内容
   */
  private static processTextContent(text: string): string {
    // 转义ASS特殊字符
    return text
      .replace(/\\/g, "\\\\")
      .replace(/\{/g, "\\{")
      .replace(/\}/g, "\\}")
      .replace(/\n/g, "\\N");
  }

  /**
   * 生成样式定义
   */
  private static generateStyleDefinition(
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): string {
    const fontName = style.fontFamily || "Arial";
    const fontSize = style.fontSize || 24;
    const primaryColor = this.convertColorToASS(style.fontColor || "#FFFFFF");
    const outlineColor = this.convertColorToASS(style.strokeColor || "#000000");
    const outlineWidth = style.strokeWidth || 0;

    // 计算对齐方式
    let alignment = 2; // 默认底部居中
    if (style.textAlign === "left") alignment = 1;
    else if (style.textAlign === "right") alignment = 3;

    return `Style: Default,${fontName},${fontSize},${primaryColor},${primaryColor},${outlineColor},&H00000000,0,0,0,0,100,100,0,0,1,${outlineWidth},0,${alignment},0,0,0,1\n`;
  }

  /**
   * 构建完整的样式标签
   */
  private static buildCompleteStyleTags(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    const tags: string[] = [];

    // 位置标签
    const positionTags = this.generatePositionTags(
      style,
      canvasWidth,
      canvasHeight
    );
    if (positionTags) tags.push(positionTags);

    // 字体样式标签
    const fontStyleTags = this.generateFontStyleTags(style);
    if (fontStyleTags) tags.push(fontStyleTags);

    // 颜色标签
    const colorTags = this.generateColorTags(style);
    if (colorTags) tags.push(colorTags);

    // 描边标签
    const strokeTags = this.generateStrokeTags(style);
    if (strokeTags) tags.push(strokeTags);

    // 阴影标签
    const shadowTags = this.generateShadowTags(style);
    if (shadowTags) tags.push(shadowTags);

    // 字符间距标签
    const charSpacingTags = this.generateCharSpacingTags(style);
    if (charSpacingTags) tags.push(charSpacingTags);

    // 行高标签
    const lineHeightTags = this.generateLineHeightTags(style);
    if (lineHeightTags) tags.push(lineHeightTags);

    // 透明度标签
    const opacityTags = this.generateOpacityTags(style);
    if (opacityTags) tags.push(opacityTags);

    return tags.join("");
  }

  /**
   * 生成位置标签
   */
  private static generatePositionTags(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!canvasWidth || !canvasHeight) {
      return "";
    }

    const centerX = canvasWidth / 2;
    const centerY = canvasHeight * 0.85;

    let x = centerX + (style.positionX || 0);
    let y = centerY + (style.positionY || 0);

    return `{\\pos(${Math.round(x)},${Math.round(y)})}`;
  }

  /**
   * 生成阴影标签
   */
  private static generateShadowTags(style: CaptionStyle): string {
    if (!style.shadowBlur && !style.shadowOffsetX && !style.shadowOffsetY) {
      return "";
    }

    const shadowColor = this.convertColorToASS(style.shadowColor);
    const offsetX = Math.round(style.shadowOffsetX || 0);
    const offsetY = Math.round(style.shadowOffsetY || 0);

    return `{\\4c${shadowColor}\\shad${Math.max(
      Math.abs(offsetX),
      Math.abs(offsetY)
    )}}`;
  }

  /**
   * 生成字符间距标签
   */
  private static generateCharSpacingTags(style: CaptionStyle): string {
    if (!style.charSpacing || style.charSpacing === 0) {
      return "";
    }

    const spacing = Math.round(style.charSpacing);
    return `{\\fsp${spacing}}`;
  }

  /**
   * 生成字体样式标签
   */
  private static generateFontStyleTags(style: CaptionStyle): string {
    let styleTags = "";

    if (style.styles && style.styles.length > 0) {
      if (style.styles.includes("bold") || style.fontWeight >= 700) {
        styleTags += "\\b1";
      }
      if (style.styles.includes("italic")) {
        styleTags += "\\i1";
      }
    }

    return styleTags ? `{${styleTags}}` : "";
  }

  /**
   * 生成颜色标签
   */
  private static generateColorTags(style: CaptionStyle): string {
    if (
      style.useGradient &&
      style.gradientColors &&
      style.gradientColors.length >= 2
    ) {
      const color1 = this.convertColorToASS(style.gradientColors[0]);
      const color2 = this.convertColorToASS(style.gradientColors[1]);
      return `{\\1c${color1}\\3c${color2}}`;
    } else {
      const color = this.convertColorToASS(style.fontColor || "#FFFFFF");
      return `{\\1c${color}}`;
    }
  }

  /**
   * 生成描边标签
   */
  private static generateStrokeTags(style: CaptionStyle): string {
    if (!style.strokeWidth || style.strokeWidth <= 0) {
      return "";
    }

    const strokeColor = this.convertColorToASS(style.strokeColor || "#000000");
    return `{\\bord${style.strokeWidth}\\3c${strokeColor}}`;
  }

  /**
   * 生成行高标签
   */
  private static generateLineHeightTags(style: CaptionStyle): string {
    if (!style.lineHeight || style.lineHeight === 1) {
      return "";
    }

    const spacing = Math.round((style.lineHeight - 1) * 100);
    return `{\\fsp${spacing}}`;
  }

  /**
   * 生成透明度标签
   */
  private static generateOpacityTags(style: CaptionStyle): string {
    const opacity = (style as any).opacity ?? 1;

    if (opacity >= 1) {
      return "";
    }

    const assOpacity = Math.round((1 - opacity) * 255);
    return `{\\1a&H${assOpacity.toString(16).padStart(2, "0").toUpperCase()}&}`;
  }

  /**
   * 生成圆角矩形绘图命令
   */
  private static generateRoundedRectangleDrawing(
    left: number,
    top: number,
    right: number,
    bottom: number,
    radius: number,
    color: string,
    alphaTag: string = ""
  ): string {
    // 简化的圆角矩形绘制 - 使用直角矩形，因为ASS的圆角绘制比较复杂
    // 在视频中，直角矩形的视觉效果通常是可接受的
    return `{\\1c${color}\\3c${color}${alphaTag}\\p1}m ${left} ${top} l ${right} ${top} l ${right} ${bottom} l ${left} ${bottom}{\\p0}`;
  }

  /**
   * 将颜色转换为ASS格式，支持hex和rgba格式
   */
  private static convertColorToASS(color: string): string {
    if (!color || color === "transparent") {
      return "&H00000000&"; // 透明色
    }

    // 处理rgba格式
    if (color.startsWith("rgba(")) {
      const rgbaMatch = color.match(
        /rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/
      );
      if (rgbaMatch) {
        const [, r, g, b, a] = rgbaMatch;
        const rHex = parseInt(r).toString(16).padStart(2, "0");
        const gHex = parseInt(g).toString(16).padStart(2, "0");
        const bHex = parseInt(b).toString(16).padStart(2, "0");
        // ASS中透明度是反向的：0=不透明，255=透明
        const alpha = Math.round((1 - parseFloat(a)) * 255);
        const aHex = alpha.toString(16).padStart(2, "0");
        return `&H${aHex}${bHex}${gHex}${rHex}&`;
      }
    }

    // 处理rgb格式
    if (color.startsWith("rgb(")) {
      const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
      if (rgbMatch) {
        const [, r, g, b] = rgbMatch;
        const rHex = parseInt(r).toString(16).padStart(2, "0");
        const gHex = parseInt(g).toString(16).padStart(2, "0");
        const bHex = parseInt(b).toString(16).padStart(2, "0");
        return `&H${bHex}${gHex}${rHex}&`;
      }
    }

    // 处理hex格式
    const hex = color.replace("#", "");
    const fullHex =
      hex.length === 3
        ? hex
            .split("")
            .map((c) => c + c)
            .join("")
        : hex;

    if (fullHex.length !== 6) {
      return "&HFFFFFF&"; // 默认白色
    }

    const r = fullHex.substring(0, 2);
    const g = fullHex.substring(2, 4);
    const b = fullHex.substring(4, 6);

    return `&H${b}${g}${r}&`;
  }

  /**
   * 生成ASS文件头部
   */
  private static generateASSHeader(
    canvasWidth: number,
    canvasHeight: number
  ): string {
    return `[Script Info]
Title: Generated Subtitles
ScriptType: v4.00+
PlayResX: ${canvasWidth || 1920}
PlayResY: ${canvasHeight || 1080}
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
`;
  }
}
